package com.evotek.notification.infrastructure.persistence.entity;

import java.util.Map;
import java.util.UUID;

import com.evo.common.entity.AuditEntity;
import com.evotek.notification.infrastructure.support.enums.NotificationStatus;
import jakarta.persistence.*;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EnableJpaAuditing
@EntityListeners(AuditingEntityListener.class)
@Table(name = "notification")
public class NotificationEntity extends AuditEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id")
    private UUID id;

    @Column(name = "title")
    private String title;

    @Column(name = "body")
    private String body;

    @Column(name = "topic")
    private String topic;

    @Column(name = "user_id")
    private UUID userId;

    @Column(name = "status")
    private NotificationStatus status;
}

package com.evotek.notification.application.service.push.impl.command;

import java.util.Map;
import java.util.concurrent.ExecutionException;

import com.evo.common.exception.ResponseException;
import com.evotek.notification.infrastructure.support.exception.BadRequestError;
import org.springframework.stereotype.Service;

import com.evo.common.dto.event.PushNotificationEvent;
import com.google.firebase.FirebaseApp;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.Message;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
@RequiredArgsConstructor
public class FirebaseNotificationService {
    public void sendNotificationToToken(PushNotificationEvent pushNotificationEvent) {
        try {
            Message message = buildNotificationMessage(pushNotificationEvent);
            sendAndGetResponse(message);
        } catch (Exception e) {
            throw new ResponseException(BadRequestError.FIREBASE_SEND_NOTIFICATION_FAILED);
        }
    }

    public void sendNotificationToTopic(PushNotificationEvent pushNotificationEvent) {
        try {
            Message message = buildNotificationMessageForTopic(pushNotificationEvent);
            sendAndGetResponse(message);
        } catch (Exception e) {
            throw new ResponseException(BadRequestError.FIREBASE_SEND_NOTIFICATION_FAILED);
        }
    }

    private Message buildNotificationMessage(PushNotificationEvent pushNotificationEvent) {

        return Message.builder()
                .setToken(pushNotificationEvent.getToken())
                .setNotification(com.google.firebase.messaging.Notification.builder()
                        .setTitle(pushNotificationEvent.getTitle())
                        .setBody(pushNotificationEvent.getBody())
                        .build())
                .putAllData(pushNotificationEvent.getData() != null ? pushNotificationEvent.getData() : Map.of())
                .build();
    }

    private Message buildNotificationMessageForTopic(PushNotificationEvent pushNotificationEvent) {
        return Message.builder()
                .setTopic(pushNotificationEvent.getTopic())
                .setNotification(com.google.firebase.messaging.Notification.builder()
                        .setTitle(pushNotificationEvent.getTitle())
                        .setBody(pushNotificationEvent.getBody())
                        .build())
                .putAllData(pushNotificationEvent.getData() != null ? pushNotificationEvent.getData() : Map.of())
                .build();
    }

    private String sendAndGetResponse(Message message) throws ExecutionException, InterruptedException {
        return FirebaseMessaging.getInstance(FirebaseApp.getInstance("my-app"))
                .sendAsync(message)
                .get();
    }
}

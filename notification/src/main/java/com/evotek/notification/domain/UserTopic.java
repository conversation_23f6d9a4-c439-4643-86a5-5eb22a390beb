package com.evotek.notification.domain;

import java.util.UUID;

import com.evo.common.entity.AuditEntity;
import com.evotek.notification.domain.command.CreateUserTopicCmd;

import jakarta.persistence.EntityListeners;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserTopic {
    private UUID id;
    private UUID userId;
    private String topic;
    private boolean enabled;

    public UserTopic(CreateUserTopicCmd cmd) {
        this.userId = cmd.getUserId();
        this.topic = cmd.getTopic();
        this.enabled = cmd.isEnabled();
    }
}

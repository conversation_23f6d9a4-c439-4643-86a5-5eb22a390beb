package com.evo.profile.infrastructure.support.exception;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import com.evo.common.exception.ResponseException;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import feign.FeignException;
import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ErrorNormalizer {
    private final ObjectMapper objectMapper;
    private final Map<String, BadRequestError> errorCodeMap;

    public ErrorNormalizer() {
        objectMapper = new ObjectMapper();
        errorCodeMap = new HashMap<>();

        errorCodeMap.put("User exists with same username", BadRequestError.USER_EXISTED);
        errorCodeMap.put("User exists with same email", BadRequestError.MAIL_EXISTED);
        errorCodeMap.put("User name is missing", BadRequestError.USERNAME_IS_MISSING);
    }

    public ResponseException handleKeyCloakException(FeignException exception) {
        try {
            log.warn("Cannot complete request", exception);
            var response = objectMapper.readValue(exception.contentUTF8(), KeyCloakError.class);

            if (Objects.nonNull(response.getErrorMessage())
                    && Objects.nonNull(errorCodeMap.get(response.getErrorMessage()))) {
                return new ResponseException(errorCodeMap.get(response.getErrorMessage()));
            }
        } catch (JsonProcessingException e) {
            log.error("Cannot deserialize content", e);
        }

        return new ResponseException(BadRequestError.UNCATEGORIZED_EXCEPTION);
    }
}

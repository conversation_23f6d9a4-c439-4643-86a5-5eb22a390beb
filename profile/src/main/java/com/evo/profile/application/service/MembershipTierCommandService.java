package com.evo.profile.application.service;

import java.util.UUID;

import com.evo.common.dto.response.MembershipTierDTO;
import com.evo.profile.application.dto.request.CreateOrUpdateMembershipTierRequest;

public interface MembershipTierCommandService {
    MembershipTierDTO create(CreateOrUpdateMembershipTierRequest createMemberShipTierRequest);

    MembershipTierDTO update(CreateOrUpdateMembershipTierRequest updateMemberShipTierRequest);

    UUID getDefaultMembershipTierId();

    void delete(UUID id, boolean deleted);

    UUID handleMembershipTierChange(Long amount);

    void toggleVisibility(String id);
}

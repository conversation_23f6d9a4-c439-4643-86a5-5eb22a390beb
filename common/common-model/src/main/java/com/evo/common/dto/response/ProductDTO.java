package com.evo.common.dto.response;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.evo.common.enums.DiscountType;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductDTO {
    private UUID id;
    private String name;
    private Long originPrice;
    private Long discountPrice;
    private Integer discountPercent;
    private DiscountType discountType;
    private UUID categoryId;
    private Map<String, String> description;
    private String introduce; // Introduce is a short description of the product(html)
    private int weight;
    private int length;
    private int width;
    private Long totalSold;
    private BigDecimal averageRating;
    private int height;
    private Boolean hidden;
    List<ProductVariantDTO> productVariants;
    List<ProductImageDTO> productImages;
    private UUID avatarId;
}
